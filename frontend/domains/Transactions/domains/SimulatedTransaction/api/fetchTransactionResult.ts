import { get, post } from '@zeal/api/request'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { stringify } from '@zeal/toolkit/JSON'
import { object, Result, shape } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { CurrencyId, KnownCurrencies } from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { parseKnownCurrencies } from '@zeal/domains/Currency/helpers/parse'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
    TestNetwork,
} from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { getPredefinedNetworkMap } from '@zeal/domains/Network/helpers/getPredefinedNetworkMap'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { SimulatedTransaction } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { parseSimulatedTransaction } from '@zeal/domains/Transactions/domains/SimulatedTransaction/parsers/parseSimulatedTransaction'
import { getExplorerLink } from '@zeal/domains/Transactions/domains/TransactionHash/helpers/getExplorerLink'

import { fetchTransactionResultByRequest2 } from './fetchTransactionResult2'
import fixture from './result_tokens_discrepancy_fixture.json'

export type TransactionResultResponse = {
    transaction: SimulatedTransaction
    currencies: KnownCurrencies
}

export type FetchTransactionResultByRequest = (_: {
    network: PredefinedNetwork | TestNetwork
    request: Request
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    sender: Web3.address.Address
    signal?: AbortSignal
}) => Promise<TransactionResultResponse>

const parse = (input: unknown): Result<unknown, TransactionResultResponse> =>
    object(input).andThen((dto) =>
        shape({
            transaction: parseSimulatedTransaction(
                dto.transaction,
                dto.currencies
            ),
            currencies: parseKnownCurrencies(dto.currencies),
        })
    )

type Request =
    | { type: 'rpc_request'; transactionHash: Hexadecimal.Hexadecimal }
    | {
          type: 'user_operation'
          userOperationHash: Hexadecimal.Hexadecimal
          bundleTransactionHash: Hexadecimal.Hexadecimal
          sender: Web3.address.Address
      }

const isValidDiscrepancy = (
    be: SimulatedTransaction,
    fe: SimulatedTransaction
) => {
    if (be.type === 'UnknownTransaction' && fe.type === 'UnknownTransaction') {
        const beHash = Object.entries(
            be.tokens.reduce(
                (hash, token) => {
                    if (!hash[token.amount.currencyId]) {
                        hash[token.amount.currencyId] = 0n
                    }

                    switch (token.direction) {
                        case 'Send':
                            hash[token.amount.currencyId] -= token.amount.amount
                            return hash

                        case 'Receive':
                            hash[token.amount.currencyId] += token.amount.amount
                            return hash

                        default:
                            return notReachable(token.direction)
                    }
                },
                {} as Record<CurrencyId, bigint>
            )
        ).toSorted(([aId, aSum], [bId, bSum]) =>
            `${aId}-${aSum}`.localeCompare(`${bId}-${bSum}`)
        )

        const feHash = Object.entries(
            fe.tokens.reduce(
                (hash, token) => {
                    if (!hash[token.amount.currencyId]) {
                        hash[token.amount.currencyId] = 0n
                    }

                    switch (token.direction) {
                        case 'Send':
                            hash[token.amount.currencyId] -= token.amount.amount
                            return hash

                        case 'Receive':
                            hash[token.amount.currencyId] += token.amount.amount
                            return hash

                        default:
                            return notReachable(token.direction)
                    }
                },
                {} as Record<CurrencyId, bigint>
            )
        ).toSorted(([aId, aSum], [bId, bSum]) =>
            `${aId}-${aSum}`.localeCompare(`${bId}-${bSum}`)
        )

        return stringify(beHash) === stringify(feHash) // result of currency reduce is same as in old be
    }

    return false
}

window.fixture = fixture
window.discrepancies = []
window.not_unknown = []
const testOne = async (
    index: number
): Promise<
    | { type: 'ok' }
    | { type: 'not_ok'; item: any; itemIndex: number; reason: string }
> => {
    const item = fixture[index] as any
    const networkMap = getPredefinedNetworkMap()
    const network = findNetworkByHexChainId(item.network, networkMap) as any

    const txHash =
        item.request.transactionHash || item.request.bundleTransactionHash

    const old = await fetchTransactionResultByRequest({
        defaultCurrencyConfig: {
            defaultCurrency: FIAT_CURRENCIES.USD as any,
        },
        network,
        networkMap,
        networkRPCMap: {},
        request: item.request,
        sender: item.request.sender || item.sender,
    })

    const newR = await fetchTransactionResultByRequest2({
        defaultCurrencyConfig: {
            defaultCurrency: FIAT_CURRENCIES.USD as any,
        },
        network,
        networkMap,
        networkRPCMap: {},
        request: item.request,
        sender: item.request.sender || item.sender,
    })

    const tx1 = old.transaction
    const tx2 = newR.simulatedTransaction

    if (
        tx1.type === 'UnknownTransaction' &&
        tx2.type === 'UnknownTransaction'
    ) {
        const output = {
            v1: tx1.tokens
                .map((t) => t.amount)
                .toSorted((a, b) => {
                    if (a.currencyId === b.currencyId) {
                        return Number(a.amount - b.amount)
                    }
                    return a.currencyId.localeCompare(b.currencyId)
                }),
            v2: tx2.tokens
                .map((t) => t.amount)
                .toSorted((a, b) => {
                    if (a.currencyId === b.currencyId) {
                        return Number(a.amount - b.amount)
                    }
                    return a.currencyId.localeCompare(b.currencyId)
                }),
        }

        const result = stringify(output.v1) === stringify(output.v2)

        if (!result) {
            if (isValidDiscrepancy(tx1, tx2)) {
                return { type: 'ok' }
            }

            // window.discrepancies.push(index)

            // console.log(stringify(output))

            // const v1Strings = output.v1
            //     .map((t) => `${t.amount.toString()}-${t.currencyId}`)
            //     .toSorted((a, b) => a.localeCompare(b))

            // const v2Strings = output.v2
            //     .map((t) => `${t.amount.toString()}-${t.currencyId}`)
            //     .toSorted((a, b) => a.localeCompare(b))

            // const discrepancyV1notInV2 = v1Strings.filter(
            //     (s) => !v2Strings.includes(s)
            // )
            // const discrepancyV2notInV1 = v2Strings.filter(
            //     (s) => !v1Strings.includes(s)
            // )

            // const discrepancies = [
            //     ...discrepancyV1notInV2,
            //     ...discrepancyV2notInV1,
            // ]

            // const allDiscrepanciesAreNative = discrepancies.every(
            //     (d) => d.split('-')[1] === network.nativeCurrency.id
            // )

            // if (allDiscrepanciesAreNative) {
            //     console.log(
            //         'discrepancy! allDiscrepanciesAreNative',
            //         txHash,
            //         network.hexChainId
            //     )
            //     return false
            // }

            // const auraTokens = new Set([
            //     'Gnosis|0x1509706a6c66ca549ff0cb464de88231ddbe213b',
            // ])

            // console.log('v1strings', v1Strings)
            // console.log('v2strings', v2Strings)
            // console.log('discrepancyV1notInV2', discrepancyV1notInV2)
            // console.log('discrepancyV2notInV1', discrepancyV2notInV1)
            // console.log('discrepancies', discrepancies)

            // if (
            //     discrepancies
            //         .map((d) => d.split('-')[1])
            //         .every((t) => auraTokens.has(t))
            // ) {
            //     console.log(
            //         'discrepancy! in aura token',
            //         discrepancies[0].split('-')[1],
            //         txHash,
            //         network.hexChainId
            //     )
            //     return false
            // }

            return {
                type: 'not_ok',
                item,
                itemIndex: index,
                reason: 'unknown',
            }
        } else {
            return {
                type: 'ok',
            }
        }
    } else {
        return {
            type: 'not_ok',
            item,
            itemIndex: index,
            reason: 'not unknown',
        }
    }
}

window.testOne = testOne
window.test = async () => {
    const start = 0
    const notOk = []
    console.log('total', fixture.length)

    for (let i = start; i < fixture.length; i++) {
        const result = await testOne(i)
        switch (result.type) {
            case 'ok':
                console.log('ok!', i)
                break
            case 'not_ok':
                console.log('not ok!', i, result.itemIndex, result.reason)
                notOk.push(result)
                break
            default:
                notReachable(result)
        }
        await new Promise((resolve) => setTimeout(resolve, 500))
    }

    console.log('done!')
    console.log('not ok!', notOk, notOk.length)
    console.log('not ok!', JSON.stringify(notOk))
}

// window.testDiscrepancies = async (limit?: number) => {
//     const disc = [
//         6, 41, 56, 95, 97, 126, 129, 157, 164, 170, 181, 184, 186, 194, 207,
//         262, 267, 330, 362, 382, 395, 450, 459, 492, 507, 522, 527, 532, 545,
//         561, 577, 589, 611, 635, 662, 704, 786, 804, 889, 891, 905, 908, 915,
//         920, 921, 944, 945, 990, 991, 1006, 1009, 1013, 1014, 1026, 1027, 1030,
//         1035, 1042, 1071, 1073, 1096, 1098, 1102, 1143, 1145, 1181, 1191, 1218,
//         1231, 1266, 1268, 1269, 1286, 1297, 1310, 1311, 1312, 1316, 1317, 1322,
//         1327, 1328, 1375, 1382, 1391, 1400, 1411, 1417, 1426, 1434, 1437, 1470,
//         1477, 1478, 1479, 1480, 1481, 1501, 1506, 1507, 1520, 1539, 1540, 1547,
//         1550, 1561, 1568, 1590, 1635, 1643, 1649, 1665, 1679, 1680, 1681, 1683,
//         1697, 1701, 1703, 1709, 1711, 1715, 1726, 1748, 1749, 1755, 1762, 1770,
//         1794, 1803, 1807, 1812, 1825, 1831, 1833, 1838, 1846, 1847, 1848, 1849,
//         1870, 1871, 1873, 1879, 1880, 1885, 1886, 1887, 1888, 1897, 1899, 1900,
//         1909, 1918, 1919, 1948, 1949, 1959, 1961, 1992, 1994, 2009, 2011, 2012,
//         2013, 2031, 2044, 2046, 2049, 2055, 2057, 2072, 2074, 2086, 2089, 2091,
//         2095, 2136, 2156, 2157, 2158, 2159, 2160, 2165, 2176, 2187, 2198, 2203,
//         2209, 2233, 2235,
//     ]

//     console.log('total', disc.length)
//     for (let i = 0; i < (limit || disc.length); i++) {
//         console.log('going into ', disc[i])
//         const result = await testOne(disc[i])
//         await new Promise((resolve) => setTimeout(resolve, 100))
//     }
//     console.log('not unknown', window.not_unknown)
//     console.log('discrepancies', window.discrepancies)
//     console.log('done!')
// }

export const fetchTransactionResultByRequest: FetchTransactionResultByRequest =
    async ({
        network,
        request,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        sender,
        signal,
    }): Promise<TransactionResultResponse> => {
        const response = await (() => {
            switch (request.type) {
                case 'rpc_request':
                    return fetchRPCTransactionResult({
                        network,
                        transactionHash: request.transactionHash,
                        signal,
                    })
                case 'user_operation':
                    return fetchUserOperationResult({
                        network,
                        userOperationHash: request.userOperationHash,
                        bundleTransactionHash: request.bundleTransactionHash,
                        sender: request.sender,
                        signal,
                    })
                /* istanbul ignore next */
                default:
                    return notReachable(request)
            }
        })()

        // fetchTransactionResultByRequest2({
        //     defaultCurrencyConfig,
        //     network,
        //     networkMap,
        //     networkRPCMap,
        //     request: (() => {
        //         switch (request.type) {
        //             case 'rpc_request': {
        //                 const request2: Parameters<
        //                     typeof fetchTransactionResultByRequest2
        //                 >[0]['request'] = {
        //                     type: 'rpc_request',
        //                     transactionHash: request.transactionHash,
        //                 }
        //                 return request2
        //             }

        //             case 'user_operation': {
        //                 const request2: Parameters<
        //                     typeof fetchTransactionResultByRequest2
        //                 >[0]['request'] = {
        //                     type: 'user_operation',
        //                     userOperationHash: request.userOperationHash,
        //                     bundleTransactionHash:
        //                         request.bundleTransactionHash,
        //                 }
        //                 return request2
        //             }
        //             /* istanbul ignore next */
        //             default:
        //                 return notReachable(request)
        //         }
        //     })(),
        //     sender,
        // }).then((result2) => {
        //     const trx = response.transaction
        //     const trx2 = result2.simulatedTransaction

        //     if (trx.type !== trx2.type) {
        //         captureError(
        //             new ImperativeError(
        //                 '[simulation2] fetchTransactionResultByRequest2 type level discrepancy detected',
        //                 {
        //                     networkHexId: network.hexChainId,
        //                     request,
        //                     sender,
        //                     trx,
        //                     trx2,
        //                 }
        //             )
        //         )
        //     } else if (
        //         trx.type === 'UnknownTransaction' &&
        //         trx2.type === 'UnknownTransaction' &&
        //         trx.tokens.length !== trx2.tokens.length
        //     ) {
        //         captureError(
        //             new ImperativeError(
        //                 '[simulation2] SimulationResult tokens level discrepancy detected',
        //                 {
        //                     networkHexId: network.hexChainId,
        //                     request,
        //                     trx,
        //                     trx2,
        //                 }
        //             )
        //         )
        //     }
        // })

        return response
    }

const fetchRPCTransactionResult = async ({
    network,
    transactionHash,
    signal,
}: {
    network: PredefinedNetwork | TestNetwork
    transactionHash: string
    signal?: AbortSignal
}): Promise<TransactionResultResponse> => {
    switch (network.type) {
        case 'predefined':
            switch (network.name) {
                case 'Ethereum':
                case 'Arbitrum':
                case 'zkSync':
                case 'BSC':
                case 'Polygon':
                case 'PolygonZkevm':
                case 'Linea':
                case 'Fantom':
                case 'Optimism':
                case 'Base':
                case 'Blast':
                case 'OPBNB':
                case 'Gnosis':
                case 'Celo':
                case 'Avalanche':
                case 'Cronos':
                case 'Mantle':
                case 'Manta':
                case 'Aurora':
                    return get(
                        `/wallet/transaction/${transactionHash}/result`,
                        {
                            query: { network: network.name },
                        },
                        signal
                    ).then((data) =>
                        parse(data).getSuccessResultOrThrow(
                            'Failed to parse submitted transaction simulation'
                        )
                    )

                case 'Sonic':
                    // FIXME :: @max durty hack we need to rethink simulations before pushing custom networks out
                    return {
                        transaction: {
                            type: 'UnknownTransaction',
                            nfts: [],
                            tokens: [],
                            method: '',
                        },
                        currencies: {},
                    }
                default:
                    return notReachable(network)
            }

        case 'testnet':
            return get(
                `/wallet/transaction/${transactionHash}/result`,
                {
                    query: { network: network.name },
                },
                signal
            ).then((data) =>
                parse(data).getSuccessResultOrThrow(
                    'Failed to parse submitted transaction simulation'
                )
            )

        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}

const fetchUserOperationResult = async ({
    network,
    sender,
    userOperationHash,
    bundleTransactionHash,
    signal,
}: {
    network: PredefinedNetwork | TestNetwork
    userOperationHash: string
    bundleTransactionHash: string
    sender: Web3.address.Address
    signal?: AbortSignal
}): Promise<TransactionResultResponse> => {
    switch (network.type) {
        case 'predefined':
        case 'testnet':
            return post(
                '/wallet/user-ops-transaction/result',
                {
                    body: {
                        sender,
                        network: network.name,
                        userOpHash: userOperationHash,
                        transactionHash: bundleTransactionHash,
                    },
                },
                signal
            ).then((response) =>
                parse(response).getSuccessResultOrThrow(
                    'Failed to parse user operation result'
                )
            )
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
